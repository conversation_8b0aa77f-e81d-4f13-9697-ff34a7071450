# Subscriptions API Documentation
## Newsletter/Updates Subscription (Iyandikishe)

## Base URL
```
/api/subscriptions/
```

## Endpoints Overview

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/api/subscriptions/` | List all subscriptions | Required (Admin) |
| POST | `/api/subscriptions/` | Create new subscription | ❌ **None (Public)** |
| GET | `/api/subscriptions/{id}/` | Get specific subscription | Required |
| PATCH | `/api/subscriptions/{id}/` | Update subscription | Required |
| POST | `/api/subscriptions/unsubscribe/` | Unsubscribe by email | ❌ **None (Public)** |
| GET | `/api/subscriptions/unsubscribe/{token}/` | Unsubscribe by token | ❌ **None (Public)** |

---

## 1. Create Subscription (Public)

**POST** `/api/subscriptions/`

### Description
Subscribe to newsletter with interests selection. This is the main endpoint for the "Iyandikishe" form.

**🔓 No authentication required** - This is a public endpoint that anyone can use to subscribe.

### Request Body
```json
{
  "first_name": "Jean",
  "last_name": "Uwimana",
  "email": "<EMAIL>",
  "interests": [
    "123e4567-e89b-12d3-a456-426614174000",
    "456e7890-e89b-12d3-a456-426614174001"
  ]
}
```

### Field Requirements
- `first_name` (required) - First name
- `last_name` (required) - Last name  
- `email` (required) - Email address (must be unique)
- `interests` (optional) - Array of category IDs

### Frontend Form Example
```jsx
const SubscriptionForm = () => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    interests: []
  });
  
  const [categories, setCategories] = useState([]);

  // Load categories for interests selection
  useEffect(() => {
    const fetchCategories = async () => {
      const response = await fetch('/api/categories/');
      const data = await response.json();
      setCategories(data.results || []);
    };
    fetchCategories();
  }, []);

  const handleInterestChange = (categoryId) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(categoryId)
        ? prev.interests.filter(id => id !== categoryId)
        : [...prev.interests, categoryId]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/subscriptions/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });
      
      if (response.ok) {
        alert('Murakoze! Mwiyandikishije neza.');
        setFormData({ first_name: '', last_name: '', email: '', interests: [] });
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-4">
        <label className="block text-neutral-700 font-medium mb-2">
          Izina rya mbere
        </label>
        <input
          type="text"
          value={formData.first_name}
          onChange={(e) => setFormData({...formData, first_name: e.target.value})}
          required
        />
      </div>

      <div className="mb-4">
        <label className="block text-neutral-700 font-medium mb-2">
          Izina rya nyuma
        </label>
        <input
          type="text"
          value={formData.last_name}
          onChange={(e) => setFormData({...formData, last_name: e.target.value})}
          required
        />
      </div>

      <div className="mb-4">
        <label className="block text-neutral-700 font-medium mb-2">
          Aderesi ya email
        </label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
          required
        />
      </div>

      <div className="mb-6">
        <label className="block text-neutral-700 font-medium mb-2">
          Ibyo ukunda (Hitamo)
        </label>
        <div className="grid grid-cols-2 gap-3">
          {categories.map((category) => (
            <label key={category.id} className="flex items-center">
              <input
                type="checkbox"
                checked={formData.interests.includes(category.id)}
                onChange={() => handleInterestChange(category.id)}
                className="mr-2"
              />
              <span className="text-sm">{category.name}</span>
            </label>
          ))}
        </div>
      </div>

      <button type="submit" className="btn btn-primary">
        Iyandikishe
      </button>
    </form>
  );
};
```

### Success Response (201 Created)
```json
{
  "message": "Subscription created successfully!",
  "subscription": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "first_name": "Jean",
    "last_name": "Uwimana",
    "full_name": "Jean Uwimana",
    "email": "<EMAIL>",
    "slug": "jean-uwimana",
    "interests": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Tekinoroji",
        "slug": "technology"
      }
    ],
    "is_active": true,
    "is_confirmed": false,
    "subscribed_at": "2024-01-15T10:30:00Z"
  }
}
```

### Error Response (400 Bad Request)
```json
{
  "email": ["This email is already subscribed to our newsletter."],
  "first_name": ["This field is required."]
}
```

---

## 2. Unsubscribe by Email

**POST** `/api/subscriptions/unsubscribe/`

### Description
Unsubscribe using email address.

### Request Body
```json
{
  "email": "<EMAIL>"
}
```

### Success Response
```json
{
  "message": "Successfully unsubscribed from newsletter."
}
```

---

## 3. Unsubscribe by Token (Email Links)

**GET** `/api/subscriptions/unsubscribe/{token}/`

### Description
Unsubscribe using token from email links. This allows one-click unsubscribe.

### Example Request
```
GET /api/subscriptions/unsubscribe/ed5cfc66-c757-4bfa-b123-456789abcdef/
```

### Success Response
```json
{
  "message": "<NAME_EMAIL> from newsletter.",
  "email": "<EMAIL>"
}
```

---

## 4. List Subscriptions (Admin Only)

**GET** `/api/subscriptions/`

### Description
List all subscriptions. Requires authentication.

### Query Parameters
- `is_active` - Filter by active status (`true`, `false`)
- `is_confirmed` - Filter by confirmation status (`true`, `false`)
- `interests__name` - Filter by interest category name
- `search` - Search in first_name, last_name, email
- `ordering` - Order by fields (`subscribed_at`, `-subscribed_at`, `first_name`)

### Example Response
```json
{
  "count": 150,
  "next": "/api/subscriptions/?page=2",
  "previous": null,
  "results": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "first_name": "Jean",
      "last_name": "Uwimana",
      "full_name": "Jean Uwimana",
      "email": "<EMAIL>",
      "interests": [
        {
          "id": "456e7890-e89b-12d3-a456-426614174001",
          "name": "Tekinoroji",
          "slug": "technology"
        }
      ],
      "is_active": true,
      "is_confirmed": true,
      "subscribed_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## Integration Notes

### Email Confirmation Flow
1. User subscribes via form
2. `is_confirmed` starts as `false`
3. Send confirmation email with link
4. Update `is_confirmed` to `true` when confirmed

### Unsubscribe Links in Emails
Include unsubscribe link in newsletters:
```
https://yoursite.com/api/subscriptions/unsubscribe/{unsubscribe_token}/
```

### Categories Integration
- Use `/api/categories/` to populate interests checkboxes
- Store selected category IDs in `interests` field
- Filter content based on user interests

This API provides complete subscription management for your "Iyandikishe" functionality!
