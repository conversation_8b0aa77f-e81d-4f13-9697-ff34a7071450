# Categories Integration Guide
## Dynamic "Ibyo ukunda" (User Preferences) Implementation

This guide shows how to replace hardcoded category options with dynamic categories fetched from the API.

## API Endpoint
```
GET /api/categories/
```

## Quick Implementation

### 1. Fetch Categories
```javascript
// Fetch all available categories
const fetchCategories = async () => {
  try {
    const response = await fetch('/api/categories/');
    const data = await response.json();
    return data.results || [];
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
};
```

### 2. Replace Hardcoded HTML
**Before (Hardcoded):**
```html
<label className="block text-neutral-700 font-medium mb-2">
  Ibyo ukunda (Hitamo)
</label>
<div className="grid grid-cols-2 gap-3">
  <label className="flex items-center">
    <input type="checkbox" className="mr-2" />
    <span className="text-sm">Technology</span>
  </label>
  <label className="flex items-center">
    <input type="checkbox" className="mr-2" />
    <span className="text-sm">Health</span>
  </label>
  <!-- More hardcoded options... -->
</div>
```

**After (Dynamic):**
```jsx
const [categories, setCategories] = useState([]);
const [selectedCategories, setSelectedCategories] = useState([]);

useEffect(() => {
  const loadCategories = async () => {
    const categoriesData = await fetchCategories();
    setCategories(categoriesData);
  };
  loadCategories();
}, []);

return (
  <div className="mb-6">
    <label className="block text-neutral-700 font-medium mb-2">
      Ibyo ukunda (Hitamo)
    </label>
    <div className="grid grid-cols-2 gap-3">
      {categories.map((category) => (
        <label key={category.id} className="flex items-center">
          <input
            type="checkbox"
            value={category.id}
            checked={selectedCategories.includes(category.id)}
            onChange={(e) => {
              const categoryId = e.target.value;
              setSelectedCategories(prev => 
                e.target.checked 
                  ? [...prev, categoryId]
                  : prev.filter(id => id !== categoryId)
              );
            }}
            className="mr-2"
          />
          <span className="text-sm">{category.name}</span>
        </label>
      ))}
    </div>
    <p className="text-sm text-gray-600 mt-2">
      Hitamo ibyo ukunda kugira ngo tuguhe amakuru ajyanye n'ibyo wishaka
    </p>
  </div>
);
```

### 3. Vanilla JavaScript Implementation
```javascript
// HTML structure
const categoriesHTML = `
  <div class="mb-6">
    <label class="block text-neutral-700 font-medium mb-2">
      Ibyo ukunda (Hitamo)
    </label>
    <div id="categories-grid" class="grid grid-cols-2 gap-3">
      <!-- Categories will be loaded here -->
    </div>
    <p class="text-sm text-gray-600 mt-2">
      Hitamo ibyo ukunda kugira ngo tuguhe amakuru ajyanye n'ibyo wishaka
    </p>
  </div>
`;

// Load and render categories
async function renderCategories() {
  const container = document.getElementById('categories-grid');
  
  try {
    const response = await fetch('/api/categories/');
    const data = await response.json();
    const categories = data.results || [];
    
    container.innerHTML = categories.map(category => `
      <label class="flex items-center">
        <input 
          type="checkbox" 
          value="${category.id}" 
          name="user-categories" 
          class="mr-2"
        />
        <span class="text-sm">${category.name}</span>
      </label>
    `).join('');
    
  } catch (error) {
    console.error('Error loading categories:', error);
    container.innerHTML = '<p class="text-red-500">Failed to load categories</p>';
  }
}

// Get selected categories
function getSelectedCategories() {
  const checkboxes = document.querySelectorAll('input[name="user-categories"]:checked');
  return Array.from(checkboxes).map(checkbox => checkbox.value);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', renderCategories);
```

## API Response Format
```json
{
  "count": 8,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "slug": "technology",
      "name": "Tekinoroji",
      "description": "Ibijyanye na tekinoroji n'iterambere",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    },
    {
      "id": "456e7890-e89b-12d3-a456-426614174001",
      "slug": "health",
      "name": "Ubuzima",
      "description": "Ibijyanye n'ubuzima n'ubuvuzi",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

## Benefits of Dynamic Categories

1. **No hardcoding** - Categories are managed from the admin panel
2. **Automatic updates** - New categories appear automatically
3. **Multilingual support** - Category names can be in Kinyarwanda
4. **Consistent data** - Same categories used across articles and preferences
5. **Easy maintenance** - Add/remove categories without code changes

## Integration with Articles

Once you have user's selected categories, you can:

```javascript
// Filter articles by user's preferred categories
const userCategories = getSelectedCategories();
const categoryNames = categories
  .filter(cat => userCategories.includes(cat.id))
  .map(cat => cat.name);

// Fetch personalized articles
const personalizedArticles = await fetch(
  `/api/articles/?categories__name=${categoryNames.join(',')}`
);
```

## Error Handling

```javascript
const [categories, setCategories] = useState([]);
const [loading, setLoading] = useState(true);
const [error, setError] = useState(null);

useEffect(() => {
  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/categories/');
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      const data = await response.json();
      setCategories(data.results || []);
      setError(null);
    } catch (err) {
      setError('Could not load categories. Please try again.');
      console.error('Categories error:', err);
    } finally {
      setLoading(false);
    }
  };
  
  loadCategories();
}, []);

// Render with loading and error states
if (loading) return <div>Loading categories...</div>;
if (error) return <div className="text-red-500">{error}</div>;
```

This implementation replaces hardcoded "Ibyo ukunda" options with dynamic categories from your API!
