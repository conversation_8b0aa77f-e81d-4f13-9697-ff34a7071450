# Testimonies API Documentation

## Base URL
```
/api/testimonies/
```

## Endpoints Overview

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/api/testimonies/` | List all testimonies | Optional |
| POST | `/api/testimonies/` | Create new testimony | Required |
| GET | `/api/testimonies/{id}/` | Get specific testimony | Optional |
| PUT | `/api/testimonies/{id}/` | Update testimony | Required |
| PATCH | `/api/testimonies/{id}/` | Partial update testimony | Required |
| DELETE | `/api/testimonies/{id}/` | Unpublish testimony | Required |

---

## 1. List Testimonies

**GET** `/api/testimonies/`

### Description
Retrieve a paginated list of testimonies. Non-authenticated users only see published testimonies.

### Query Parameters
- `status` - Filter by status (`draft`, `published`)
- `search` - Search in name, title, email, content
- `ordering` - Order by fields (`name`, `created_at`, `-created_at`)
- `page` - Page number for pagination

### Example Request
```javascript
// Get published testimonies
fetch('/api/testimonies/?status=published&page=1')

// Search testimonies
fetch('/api/testimonies/?search=developer')

// Order by newest first
fetch('/api/testimonies/?ordering=-created_at')
```

### Example Response
```json
{
  "count": 25,
  "next": "/api/testimonies/?page=2",
  "previous": null,
  "results": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Jane Doe",
      "title": "Software Developer",
      "email": "<EMAIL>",
      "slug": "jane-doe",
      "content": "This organization has helped me grow professionally...",
      "image": "https://example.com/media/testimonies/images/jane.jpg",
      "status": "published",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## 2. Create Testimony

**POST** `/api/testimonies/`

### Description
Submit a new testimony. Requires authentication.

### Request Body
```json
{
  "name": "John Smith",
  "title": "Business Owner",
  "email": "<EMAIL>",
  "content": "I want to share my story about how this organization helped me...",
  "image": null,
  "status": "draft"
}
```

### Field Requirements
- `name` (required) - Full name
- `title` (optional) - Role/occupation
- `email` (required) - Contact email
- `content` (required) - Testimony story
- `image` (optional) - Profile image file
- `status` (optional) - Defaults to "draft"

### Example Frontend Form Submission
```javascript
const formData = new FormData();
formData.append('name', 'John Smith');
formData.append('title', 'Business Owner');
formData.append('email', '<EMAIL>');
formData.append('content', 'My testimony story...');
formData.append('status', 'draft');

// If image is selected
if (imageFile) {
  formData.append('image', imageFile);
}

fetch('/api/testimonies/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-token-here'
  },
  body: formData
})
.then(response => response.json())
.then(data => console.log('Success:', data));
```

### Success Response (201 Created)
```json
{
  "name": "John Smith",
  "title": "Business Owner",
  "email": "<EMAIL>",
  "content": "My testimony story...",
  "image": null,
  "status": "draft"
}
```

### Error Response (400 Bad Request)
```json
{
  "name": ["This field is required."],
  "email": ["Enter a valid email address."]
}
```

---

## 3. Get Single Testimony

**GET** `/api/testimonies/{id}/`

### Description
Retrieve a specific testimony by ID.

### Example Request
```javascript
fetch('/api/testimonies/123e4567-e89b-12d3-a456-426614174000/')
```

### Response
Same format as individual testimony in list response.

---

## 4. Update Testimony

**PUT** `/api/testimonies/{id}/` - Full update
**PATCH** `/api/testimonies/{id}/` - Partial update

### Description
Update an existing testimony. Requires authentication.

### Example Request (PATCH)
```javascript
fetch('/api/testimonies/123e4567-e89b-12d3-a456-426614174000/', {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token-here'
  },
  body: JSON.stringify({
    "status": "published"
  })
})
```

---

## 5. Unpublish Testimony

**DELETE** `/api/testimonies/{id}/`

### Description
Sets testimony status to "draft" (unpublishes it). Requires authentication.

### Example Request
```javascript
fetch('/api/testimonies/123e4567-e89b-12d3-a456-426614174000/', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer your-token-here'
  }
})
```

### Response
204 No Content

---

## Frontend Integration Examples

### React Form Component
```jsx
const TestimonyForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    title: '',
    email: '',
    content: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/testimonies/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });
      
      if (response.ok) {
        alert('Testimony submitted successfully!');
        setFormData({ name: '', title: '', email: '', content: '' });
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Amazina yawe"
        value={formData.name}
        onChange={(e) => setFormData({...formData, name: e.target.value})}
        required
      />
      <input
        type="text"
        placeholder="Uwo uriwe / Icyo ukora"
        value={formData.title}
        onChange={(e) => setFormData({...formData, title: e.target.value})}
      />
      <input
        type="email"
        placeholder="Aderesi ya email"
        value={formData.email}
        onChange={(e) => setFormData({...formData, email: e.target.value})}
        required
      />
      <textarea
        placeholder="Dusangize inkuru yawe"
        value={formData.content}
        onChange={(e) => setFormData({...formData, content: e.target.value})}
        required
      />
      <button type="submit">Ohereza ubuhamya bwawe</button>
    </form>
  );
};
```

## Error Handling

### Common HTTP Status Codes
- `200` - Success
- `201` - Created successfully
- `204` - No content (successful deletion)
- `400` - Bad request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not found
- `500` - Server error

### Validation Errors
The API returns detailed validation errors in the response body for 400 status codes.
