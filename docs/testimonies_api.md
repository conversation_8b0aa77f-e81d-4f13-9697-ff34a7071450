# Testimonies & Categories API Documentation

## Base URLs
```
/api/testimonies/
/api/categories/
```

## Endpoints Overview

### Testimonies
| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/api/testimonies/` | List all testimonies | Optional |
| POST | `/api/testimonies/` | Create new testimony | Required |
| GET | `/api/testimonies/{id}/` | Get specific testimony | Optional |
| PUT | `/api/testimonies/{id}/` | Update testimony | Required |
| PATCH | `/api/testimonies/{id}/` | Partial update testimony | Required |
| DELETE | `/api/testimonies/{id}/` | Unpublish testimony | Required |

### Categories (for "Ibyo ukunda" selection)
| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/api/categories/` | List all categories | None |
| GET | `/api/categories/{slug}/` | Get specific category | None |

---

## Categories API (for "Ibyo ukunda" - User Preferences)

### Get All Categories

**GET** `/api/categories/`

### Description
Fetch all available article categories for user selection. Use this to populate the "Ibyo ukunda" (favorites/interests) dropdown/checkboxes.

### Query Parameters
- `search` - Search categories by name or description
- `ordering` - Order by `name`, `-name`, `created_at`, `-created_at`
- `page` - Page number for pagination

### Example Request
```javascript
// Get all categories for user selection
fetch('/api/categories/')

// Search specific categories
fetch('/api/categories/?search=technology')
```

### Example Response
```json
{
  "count": 8,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "slug": "technology",
      "name": "Tekinoroji",
      "description": "Ibijyanye na tekinoroji n'iterambere",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    },
    {
      "id": "456e7890-e89b-12d3-a456-426614174001",
      "slug": "health",
      "name": "Ubuzima",
      "description": "Ibijyanye n'ubuzima n'ubuvuzi",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    },
    {
      "id": "789e0123-e89b-12d3-a456-426614174002",
      "slug": "education",
      "name": "Uburezi",
      "description": "Ibijyanye n'uburezi n'amashuri",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## 1. List Testimonies

**GET** `/api/testimonies/`

### Description
Retrieve a paginated list of testimonies. Non-authenticated users only see published testimonies.

### Query Parameters
- `status` - Filter by status (`draft`, `published`)
- `search` - Search in name, title, email, content
- `ordering` - Order by fields (`name`, `created_at`, `-created_at`)
- `page` - Page number for pagination

### Example Request
```javascript
// Get published testimonies
fetch('/api/testimonies/?status=published&page=1')

// Search testimonies
fetch('/api/testimonies/?search=developer')

// Order by newest first
fetch('/api/testimonies/?ordering=-created_at')
```

### Example Response
```json
{
  "count": 25,
  "next": "/api/testimonies/?page=2",
  "previous": null,
  "results": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Jane Doe",
      "title": "Software Developer",
      "email": "<EMAIL>",
      "slug": "jane-doe",
      "content": "This organization has helped me grow professionally...",
      "image": "https://example.com/media/testimonies/images/jane.jpg",
      "status": "published",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## 2. Create Testimony

**POST** `/api/testimonies/`

### Description
Submit a new testimony. Requires authentication.

### Request Body
```json
{
  "name": "John Smith",
  "title": "Business Owner",
  "email": "<EMAIL>",
  "content": "I want to share my story about how this organization helped me...",
  "image": null,
  "status": "draft"
}
```

### Field Requirements
- `name` (required) - Full name
- `title` (optional) - Role/occupation
- `email` (required) - Contact email
- `content` (required) - Testimony story
- `image` (optional) - Profile image file
- `status` (optional) - Defaults to "draft"

### Example Frontend Form Submission
```javascript
const formData = new FormData();
formData.append('name', 'John Smith');
formData.append('title', 'Business Owner');
formData.append('email', '<EMAIL>');
formData.append('content', 'My testimony story...');
formData.append('status', 'draft');

// If image is selected
if (imageFile) {
  formData.append('image', imageFile);
}

fetch('/api/testimonies/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-token-here'
  },
  body: formData
})
.then(response => response.json())
.then(data => console.log('Success:', data));
```

### Success Response (201 Created)
```json
{
  "name": "John Smith",
  "title": "Business Owner",
  "email": "<EMAIL>",
  "content": "My testimony story...",
  "image": null,
  "status": "draft"
}
```

### Error Response (400 Bad Request)
```json
{
  "name": ["This field is required."],
  "email": ["Enter a valid email address."]
}
```

---

## 3. Get Single Testimony

**GET** `/api/testimonies/{id}/`

### Description
Retrieve a specific testimony by ID.

### Example Request
```javascript
fetch('/api/testimonies/123e4567-e89b-12d3-a456-426614174000/')
```

### Response
Same format as individual testimony in list response.

---

## 4. Update Testimony

**PUT** `/api/testimonies/{id}/` - Full update
**PATCH** `/api/testimonies/{id}/` - Partial update

### Description
Update an existing testimony. Requires authentication.

### Example Request (PATCH)
```javascript
fetch('/api/testimonies/123e4567-e89b-12d3-a456-426614174000/', {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token-here'
  },
  body: JSON.stringify({
    "status": "published"
  })
})
```

---

## 5. Unpublish Testimony

**DELETE** `/api/testimonies/{id}/`

### Description
Sets testimony status to "draft" (unpublishes it). Requires authentication.

### Example Request
```javascript
fetch('/api/testimonies/123e4567-e89b-12d3-a456-426614174000/', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer your-token-here'
  }
})
```

### Response
204 No Content

---

## Frontend Integration Examples

### React Form Component with Dynamic Categories
```jsx
const TestimonyForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    title: '',
    email: '',
    content: ''
  });

  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories/');
        const data = await response.json();
        setCategories(data.results || []);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  const handleCategoryChange = (categoryId) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/testimonies/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        alert('Testimony submitted successfully!');
        setFormData({ name: '', title: '', email: '', content: '' });
        setSelectedCategories([]);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-6">
        <label className="block text-primary font-medium mb-2">Amazina yawe</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          placeholder="Injiza izina ryawe"
          required
        />
      </div>

      <div className="mb-6">
        <label className="block text-primary font-medium mb-2">Uwo uriwe / Icyo ukora</label>
        <input
          type="text"
          value={formData.title}
          onChange={(e) => setFormData({...formData, title: e.target.value})}
          placeholder="Urugero, umubyeyi / umucuruzi"
        />
      </div>

      <div className="mb-6">
        <label className="block text-primary font-medium mb-2">Aderesi ya email</label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
          placeholder="Injiza email yawe"
          required
        />
      </div>

      <div className="mb-6">
        <label className="block text-primary font-medium mb-2">Ubuhamya</label>
        <textarea
          rows="6"
          value={formData.content}
          onChange={(e) => setFormData({...formData, content: e.target.value})}
          placeholder="Dusangize inkuru yawe"
          required
        />
      </div>

      {/* Dynamic Categories Selection */}
      <div className="mb-6">
        <label className="block text-neutral-700 font-medium mb-2">
          Ibyo ukunda (Hitamo)
        </label>
        <div className="grid grid-cols-2 gap-3">
          {categories.map((category) => (
            <label key={category.id} className="flex items-center">
              <input
                type="checkbox"
                checked={selectedCategories.includes(category.id)}
                onChange={() => handleCategoryChange(category.id)}
                className="mr-2"
              />
              <span className="text-sm">{category.name}</span>
            </label>
          ))}
        </div>
        <p className="text-sm text-gray-600 mt-2">
          Hitamo ibyo ukunda kugira ngo tuguhe amakuru ajyanye n'ibyo wishaka
        </p>
      </div>

      <button type="submit" className="btn btn-primary w-full md:w-auto">
        Ohereza ubuhamya bwawe
      </button>
    </form>
  );
};
```

### Vanilla JavaScript Example
```javascript
// Fetch and populate categories
async function loadCategories() {
  try {
    const response = await fetch('/api/categories/');
    const data = await response.json();
    const categories = data.results || [];

    const container = document.getElementById('categories-container');
    container.innerHTML = '';

    categories.forEach(category => {
      const label = document.createElement('label');
      label.className = 'flex items-center mb-2';
      label.innerHTML = `
        <input type="checkbox" value="${category.id}" name="categories" class="mr-2">
        <span>${category.name}</span>
      `;
      container.appendChild(label);
    });
  } catch (error) {
    console.error('Error loading categories:', error);
  }
}

// Get selected category IDs
function getSelectedCategories() {
  const checkboxes = document.querySelectorAll('input[name="categories"]:checked');
  return Array.from(checkboxes).map(cb => cb.value);
}

// Load categories when page loads
document.addEventListener('DOMContentLoaded', loadCategories);
```

### HTML Structure for Categories
```html
<div class="mb-6">
  <label class="block text-neutral-700 font-medium mb-2">
    Ibyo ukunda (Hitamo)
  </label>
  <div id="categories-container" class="grid grid-cols-2 gap-3">
    <!-- Categories will be populated here by JavaScript -->
  </div>
  <p class="text-sm text-gray-600 mt-2">
    Hitamo ibyo ukunda kugira ngo tuguhe amakuru ajyanye n'ibyo wishaka
  </p>
</div>
```

## Usage Notes

### Category Selection Best Practices
1. **Load categories on page load** - Fetch categories when the form loads
2. **Handle loading states** - Show loading indicator while fetching categories
3. **Store selected IDs** - Keep track of selected category IDs for future use
4. **Graceful fallback** - Handle cases where categories fail to load
5. **User feedback** - Show confirmation when preferences are saved

### Integration with Articles
Once you have selected category IDs, you can:
- Filter articles by category: `GET /api/articles/?categories__name=CategoryName`
- Get articles in specific category: `GET /api/articles/category/{category-slug}/`
- Personalize content based on user preferences

## Error Handling

### Common HTTP Status Codes
- `200` - Success
- `201` - Created successfully
- `204` - No content (successful deletion)
- `400` - Bad request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not found
- `500` - Server error

### Validation Errors
The API returns detailed validation errors in the response body for 400 status codes.
