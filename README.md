# Umugore Uzashimwa API Documentation

## Base URL
`https://umugore-uzashimwa-backend.onrender.com/api/`

## Authentication
No authentication required for read operations.

---

# Categories

## List All Categories
`GET categories/`

### Query Parameters
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `page` | integer | Page number | 1 |
| `page_size` | integer | Items per page (max 100) | 10 |
| `search` | string | Search by name or description | - |
| `ordering` | string | Sort results (`name`, `-name`, `created_at`, `-created_at`) | `name` |

### Response
```json
{
  "count": 15,
  "total_pages": 2,
  "links": {
    "next": "https://umugore-uzashimwa-backend.onrender.com/api/base/categories/?page=2",
    "previous": null
  },
  "results": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "name": "Technology",
      "slug": "technology",
      "description": "Articles about modern technology",
      "created_at": "2023-01-15T08:30:00Z",
      "updated_at": "2023-01-15T08:30:00Z"
    }
  ]
}
```

## Get Category Details
`GET /base/categories/{slug}/`

### Response
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "Technology",
  "slug": "technology",
  "description": "Articles about modern technology",
  "created_at": "2023-01-15T08:30:00Z",
  "updated_at": "2023-01-20T10:15:00Z"
}
```

---

# Articles

## List All Articles
`GET /articles/`

### Query Parameters
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `categories` | string | Filter by category slugs (comma-separated) | - |
| `status` | string | Filter by status (`published`, `draft`) | `published` for anonymous users |
| `search` | string | Search in title/description/content | - |
| `ordering` | string | Sort results (`-created_at`, `title`, etc.) | `-created_at` |

### Filter by Categories Example
```http
GET /api/articles/?categories=technology,health
```

### Response
```json
{
  "count": 25,
  "total_pages": 3,
  "links": {
    "next": "https://umugore-uzashimwa-backend.onrender.com/api/articles/?page=2",
    "previous": null
  },
  "results": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "title": "AI in Healthcare",
      "slug": "ai-healthcare",
      "categories": [
        {"slug": "technology", "name": "Technology"},
        {"slug": "health", "name": "Health"}
      ],
      "status": "published",
      "created_at": "2023-02-10T14:20:00Z"
    }
  ]
}
```

## Get Article Details
`GET /articles/{slug}/`

### Response
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "title": "AI in Healthcare",
  "slug": "ai-healthcare",
  "content": "<p>Full article content...</p>",
  "categories": [
    {"slug": "technology", "name": "Technology"},
    {"slug": "health", "name": "Health"}
  ],
  "authors": [
    {"name": "Dr. Jane Doe", "slug": "jane-doe"}
  ],
  "status": "published",
  "created_at": "2023-02-10T14:20:00Z"
}
```

---

# Testimonies

## List All Testimonies
`GET /testimonies/`

### Query Parameters
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `status` | string | Filter by status (`published`, `draft`) | `published` for anonymous users |
| `ordering` | string | Sort results (`-created_at`, `name`) | `-created_at` |

### Response
```json
{
  "count": 10,
  "results": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "name": "John Smith",
      "content": "This platform changed my life...",
      "status": "published",
      "created_at": "2023-03-05T09:15:00Z"
    }
  ]
}
```

## Get Testimony Details
`GET /testimonies/{id}/`

### Response
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "John Smith",
  "email": "<EMAIL>",
  "content": "This platform changed my life...",
  "image": "https://impano-db.fra1.cdn.digitaloceanspaces.com/umugore_uzashimwa/media/john-smith.jpg",
  "status": "published",
  "created_at": "2023-03-05T09:15:00Z"
}
```

---

# Partners

## List All Partners
`GET /partners/`

### Query Parameters
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `status` | string | Filter by status (`published`, `draft`) | `published` for anonymous users |
| `categories` | string | Filter by category slugs | - |

### Response
```json
{
  "count": 8,
  "results": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "name": "Tech Solutions Inc.",
      "slug": "tech-solutions",
      "website": "https://techsolutions.example.com",
      "status": "published"
    }
  ]
}
```

## Get Partner Details
`GET /partners/{slug}/`

### Response
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "Tech Solutions Inc.",
  "slug": "tech-solutions",
  "description": "Leading technology provider...",
  "website": "https://techsolutions.example.com",
  "categories": [
    {"slug": "technology", "name": "Technology"}
  ],
  "status": "published"
}
```

---

# Authors

## List All Authors
`GET /accounts/authors/`

### Response
```json
{
  "count": 5,
  "results": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "name": "Dr. Jane Doe",
      "slug": "jane-doe",
      "bio": "Medical researcher...",
      "image": "https://impano-db.fra1.cdn.digitaloceanspaces.com/umugore_uzashimwa/media/authors/jane-doe.jpeg"
    }
  ]
}
```

## Get Author Details
`GET /accounts/authors/{id}/`

### Response
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "Dr. Jane Doe",
  "slug": "jane-doe",
  "bio": "Medical researcher with 10 years experience...",
  "image": "https://impano-db.fra1.cdn.digitaloceanspaces.com/umugore_uzashimwa/media/authors/jane-doe.jpeg",
  "articles": [
    {
      "title": "AI in Healthcare",
      "slug": "ai-healthcare",
      "published_date": "2023-02-10"
    }
  ]
}
```

## Notes
1. All timestamp fields are in UTC
2. URL patterns:
   - Categories: `/base/categories/{slug}/`
   - Articles: `/articles/{slug}/` 
   - Testimonies: `/testimonies/{id}/`
   - Partners: `/partners/{slug}/`
   - Authors: <AUTHORS>
3. Filter articles by categories using `?categories=slug1,slug2`
4. Authenticated users see draft content
