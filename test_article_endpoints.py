#!/usr/bin/env python3
"""
Test script to verify the new article endpoints work correctly.
This script tests the ArticlesByAuthorAPIView and ArticleByCategoryAPIView endpoints.
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status
import json

# Add the project root to Python path
sys.path.append('/Users/<USER>/Documents/pro/umugore_uzashimwa_backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from articles.models import Article
from accounts.models import Author
from base.models import Category


def test_endpoints():
    """Test the new article endpoints"""
    
    # Create test client
    client = APIClient()
    
    # Create test user
    user = User.objects.create_user(username='testuser', password='testpass')
    
    # Create test author
    author = Author.objects.create(
        name='Test Author',
        bio='Test bio',
        created_by=user
    )
    
    # Create test category
    category = Category.objects.create(
        name='Test Category',
        description='Test description',
        created_by=user
    )
    
    # Create test article
    article = Article.objects.create(
        title='Test Article',
        content='Test content',
        status='published',
        created_by=user
    )
    article.authors.add(author)
    article.categories.add(category)
    
    print("Test data created successfully!")
    print(f"Author ID: {author.id}")
    print(f"Category slug: {category.slug}")
    print(f"Article: {article.title}")
    
    # Test articles by author endpoint
    print("\n--- Testing Articles by Author endpoint ---")
    url = f'/api/articles/author/{author.id}/'
    print(f"Testing URL: {url}")
    
    response = client.get(url)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
        print("✅ Articles by Author endpoint works!")
    else:
        print(f"❌ Articles by Author endpoint failed: {response.content}")
    
    # Test articles by category endpoint
    print("\n--- Testing Articles by Category endpoint ---")
    url = f'/api/articles/category/{category.slug}/'
    print(f"Testing URL: {url}")
    
    response = client.get(url)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
        print("✅ Articles by Category endpoint works!")
    else:
        print(f"❌ Articles by Category endpoint failed: {response.content}")
    
    # Clean up
    article.delete()
    author.delete()
    category.delete()
    user.delete()
    
    print("\n✅ Test completed and cleaned up!")


if __name__ == '__main__':
    test_endpoints()
