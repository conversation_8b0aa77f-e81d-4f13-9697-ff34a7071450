from django.urls import path
from subscriptions.views import (
    SubscriptionListCreateAPIView,
    SubscriptionRetrieveUpdateAPIView,
    UnsubscribeAPIView,
    UnsubscribeByTokenAPIView
)

urlpatterns = [
    path('', SubscriptionListCreateAPIView.as_view(), name='subscription-list-create'),
    path('<uuid:pk>/', SubscriptionRetrieveUpdateAPIView.as_view(), name='subscription-retrieve-update'),
    path('unsubscribe/', UnsubscribeAPIView.as_view(), name='unsubscribe'),
    path('unsubscribe/<str:token>/', UnsubscribeByTokenAPIView.as_view(), name='unsubscribe-by-token'),
]
