# articles/urls.py
from django.urls import path
from articles.views import (
    ArticleListCreateAPIView,
    ArticleRetrieveUpdateUnpublishAPIView,
    ArticlesByAuthorAPIView,
    ArticleByCategoryAPIView
)

urlpatterns = [
    path('', ArticleListCreateAPIView.as_view(), name='article-list-create'),
    path('author/<uuid:author_id>/', ArticlesByAuthorAPIView.as_view(), name='articles-by-author'),
    path('category/<str:category_slug>/', ArticleByCategoryAPIView.as_view(), name='articles-by-category'),
    path('<str:slug>/', ArticleRetrieveUpdateUnpublishAPIView.as_view(), name='article-retrieve-update-unpublish'),
]