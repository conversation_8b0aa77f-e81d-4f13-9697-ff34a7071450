# articles/views.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter

from articles.models import Article
from articles.serializers import (
    ArticleListSerializer,
    ArticleCreateUpdateSerializer,
    GetArticleSerializer
)
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from articles.pagination import ArticlePagination

class ArticleListCreateAPIView(APIView):
    """
    API View for listing all published articles and creating new articles.
    
    GET: Returns a paginated list of all published articles
    POST: Creates a new article
    """
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['categories__name', 'authors__name']
    search_fields = ['title', 'short_description', 'content']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    pagination_class = ArticlePagination

    def get_paginated_response(self, data):
        """
        Return a paginated style `Response` object for the given output data.
        """
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(data, self.request)
        if page is not None:
            serializer = ArticleListSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        serializer = ArticleListSerializer(data, many=True)
        return Response(serializer.data)

    def get_queryset(self):
        """
        Returns queryset of published articles only for non-authenticated users.
        Authenticated users can see all articles except deleted ones.
        """
        queryset = Article.objects.filter(is_deleted=False)
        
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(status='published')
            
        return queryset

    def get(self, request, *args, **kwargs):
        """
        Handle GET request to list articles with optional filtering and pagination.
        """
        queryset = self.get_queryset()
        
        # Apply filters
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(request, queryset, self)
            
        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = ArticleListSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
            
        serializer = ArticleListSerializer(queryset, many=True)
        return Response(serializer.data)
    

    def post(self, request, *args, **kwargs):
        """
        Handle POST request to create a new article.
        """
        serializer = ArticleCreateUpdateSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(created_by=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ArticleRetrieveUpdateUnpublishAPIView(APIView):
    """
    API View for retrieving, updating, and unpublishing a single article by slug.
    
    GET: Retrieve a single article
    PUT: Fully update an article
    PATCH: Partially update an article
    DELETE: Unpublish an article (set status to draft)
    """
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_object(self, slug):
        """
        Retrieve an article by slug, checking permissions.
        """
        queryset = Article.objects.filter(is_deleted=False)
        
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(status='published')
            
        obj = get_object_or_404(queryset, slug=slug)
        self.check_object_permissions(self.request, obj)
        return obj

    def get(self, request, slug, *args, **kwargs):
        """
        Retrieve a single article by slug.
        """
        article = self.get_object(slug)
        serializer = GetArticleSerializer(article)
        return Response(serializer.data)

    def put(self, request, slug, *args, **kwargs):
        """
        Fully update an article.
        """
        article = self.get_object(slug)
        serializer = ArticleCreateUpdateSerializer(article, data=request.data)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, slug, *args, **kwargs):
        """
        Partially update an article.
        """
        article = self.get_object(slug)
        serializer = ArticleCreateUpdateSerializer(article, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug, *args, **kwargs):
        """
        Unpublish an article by setting its status to draft.
        """
        article = self.get_object(slug)
        article.status = 'draft'
        article.updated_by = request.user
        article.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ArticlesByAuthorAPIView(APIView):
    """
    API View for listing all articles by a specific author.
    
    GET: Returns a paginated list of all articles by the author
    """
    permission_classes = [IsAuthenticatedOrReadOnly]
    pagination_class = ArticlePagination

    def get_queryset(self, author_slug):
        """
        Returns queryset of articles by the author.
        """
        queryset = Article.objects.filter(is_deleted=False, authors__slug=author_slug)
        return queryset

    def get(self, request, author_slug, *args, **kwargs):
        """
        Handle GET request to list articles by the author with optional filtering and pagination.
        """
        queryset = self.get_queryset(author_slug)
        
        # Apply filters
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(request, queryset, self)
            
        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = ArticleListSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
            
        serializer = ArticleListSerializer(queryset, many=True)
        return Response(serializer.data) if serializer else None


class ArticleByCategoryAPIView(APIView):
    """
    API View for listing all articles in a specific category.
    
    GET: Returns a paginated list of all articles in the category
    """
    permission_classes = [IsAuthenticatedOrReadOnly]
    pagination_class = ArticlePagination

    def get_queryset(self, category_slug):
        """
        Returns queryset of articles in the category.
        """
        queryset = Article.objects.filter(is_deleted=False, categories__slug=category_slug)
        return queryset
    
    def get(self, request, category_slug, *args, **kwargs):
        """
        Handle GET request to list articles in the category with optional filtering and pagination.
        """
        queryset = self.get_queryset(category_slug)
        
        # Apply filters
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(request, queryset, self)
            
        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = ArticleListSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
            
        serializer = ArticleListSerializer(queryset, many=True)
        return Response(serializer.data) is not None