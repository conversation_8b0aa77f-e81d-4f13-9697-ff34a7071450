# Generated by Django 5.2 on 2025-05-20 18:13

import base.models
import django.db.models.deletion
import django_ckeditor_5.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0002_alter_author_created_by_alter_author_deleted_by_and_more'),
        ('base', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
                ('short_description', models.TextField(blank=True, help_text='Short description of the article (max 500 characters)', max_length=500, null=True)),
                ('content', django_ckeditor_5.fields.CKEditor5Field(verbose_name='Content')),
                ('featured_image', models.ImageField(blank=True, null=True, storage=base.models.MediaStorage(), upload_to='articles/featured_images/')),
                ('original_article_url', models.URLField(blank=True, help_text='URL of the original article before translation', max_length=255, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published')], default='draft', max_length=20)),
                ('authors', models.ManyToManyField(blank=True, related_name='article_authors', to='accounts.author')),
                ('categories', models.ManyToManyField(blank=True, related_name='article_categories', to='base.category')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Article',
                'verbose_name_plural': 'Articles',
                'ordering': ['created_at'],
            },
        ),
    ]
