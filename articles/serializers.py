# articles/serializers.py
from rest_framework import serializers
from articles.models import Article
from accounts.models import Author
from base.models import Category
from base.serializers import CategorySerializer

class AuthorSerializer(serializers.ModelSerializer):
    """
    Serializer for Author model
    """
    class Meta:
        model = Author
        fields = ['id', 'name', 'bio', 'image']


class ArticleListSerializer(serializers.ModelSerializer):
    """
    Main Article serializer with nested relationships
    """
    authors = AuthorSerializer(many=True, read_only=True)
    categories = CategorySerializer(many=True, read_only=True)
    status = serializers.CharField(read_only=True)

    class Meta:
        model = Article
        fields = [
            'id', 'title', 'slug', 'short_description',
            'featured_image', 'categories', 'original_article_url',
            'authors', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at']

class GetArticleSerializer(serializers.ModelSerializer):
    """
    Serializer for retrieving a single article
    """
    authors = AuthorSerializer(many=True, read_only=True)
    categories = CategorySerializer(many=True, read_only=True)
    status = serializers.Char<PERSON>ield(read_only=True)

    class Meta:
        model = Article
        fields = [
            'id', 'title', 'slug', 'short_description', 'content',
            'featured_image', 'categories', 'original_article_url',
            'authors', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at']

class ArticleCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating articles with writable relationships
    """
    authors = serializers.PrimaryKeyRelatedField(
        queryset=Author.objects.all(),
        many=True,
        required=False
    )
    categories = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(),
        many=True,
        required=False
    )

    class Meta:
        model = Article
        fields = [
            'title', 'short_description', 'content', 'featured_image',
            'categories', 'original_article_url', 'authors', 'status'
        ]

    def create(self, validated_data):
        authors = validated_data.pop('authors', [])
        categories = validated_data.pop('categories', [])
        article = Article.objects.create(**validated_data)
        article.authors.set(authors)
        article.categories.set(categories)
        return article

    def update(self, instance, validated_data):
        authors = validated_data.pop('authors', None)
        categories = validated_data.pop('categories', None)
        
        instance = super().update(instance, validated_data)
        
        if authors is not None:
            instance.authors.set(authors)
        if categories is not None:
            instance.categories.set(categories)
            
        return instance