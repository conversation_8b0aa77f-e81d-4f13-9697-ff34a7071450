from django.db import models
from accounts.models import Author
from base.models import BaseModel, MediaStorage, Category
from django_ckeditor_5.fields import CKE<PERSON>or5<PERSON><PERSON>
from django.utils.text import slugify

# Article Model

class Article(BaseModel):
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    short_description = models.TextField(
        max_length=500, null=True, blank=True,
        help_text="Short description of the article (max 500 characters)" 
    )
    content = CKEditor5Field('Content', config_name='extends')
    featured_image = models.ImageField(
        storage=MediaStorage(),
        upload_to='articles/featured_images/',
        blank=True,
        null=True
    )
    categories = models.ManyToManyField('base.Category', blank=True, related_name='article_categories')
    original_article_url = models.URLField(
        max_length=255,
        blank=True,
        null=True,
        help_text="URL of the original article before translation"
    )
    authors = models.ManyToManyField('accounts.Author', blank=True, related_name='article_authors')
    status = models.CharField(
        max_length=20,
        choices=[('draft', 'Draft'), ('published', 'Published')],
        default='draft'
    )

    def save(self, *args, **kwargs):
        # Generate slug if not exists
        if not self.slug:
            self.slug = slugify(self.title)
        # Save the post
        super().save(*args, **kwargs)
        
        
    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = 'Article'
        verbose_name_plural = 'Articles'
        ordering = ['created_at']
