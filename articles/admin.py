from django.contrib import admin
from .models import Article

@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'status')
    search_fields = ('title', 'short_description')
    list_filter = ('status', 'created_at', 'authors', 'categories')
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_deleted=False)
# Register your models here.
