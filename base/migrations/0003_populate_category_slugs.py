# Generated by Django 5.2 on 2025-05-25 19:10

# base/migrations/0003_populate_category_slugs.py
from django.db import migrations
from django.utils.text import slugify

def generate_unique_slug(model, name):
    base_slug = slugify(name)
    slug = base_slug
    counter = 1
    while model.objects.filter(slug=slug).exists():
        slug = f"{base_slug}-{counter}"
        counter += 1
    return slug

def populate_slugs(apps, schema_editor):
    Category = apps.get_model('base', 'Category')
    for category in Category.objects.all():
        category.slug = generate_unique_slug(Category, category.name)
        category.save()

class Migration(migrations.Migration):
    dependencies = [
        ('base', '0002_add_slug_field_non_unique'),
    ]

    operations = [
        migrations.RunPython(populate_slugs),
    ]