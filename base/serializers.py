from rest_framework import serializers
from base.models import Category

class CategorySerializer(serializers.ModelSerializer):
    """
    Serializer for Category model - Read operations
    """
    class Meta:
        model = Category
        fields = ['id', 'slug', 'name', 'description', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

class CategoryCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating categories
    """
    class Meta:
        model = Category
        fields = ['name', 'description']

    def validate_name(self, value):
        """Ensure category name is unique"""
        if Category.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError("A category with this name already exists.")
        return value