from django.db import models
from dotenv import load_dotenv
from storages.backends.s3boto3 import S3Boto3Storage
from django.contrib.auth.models import User
import os
import uuid
from django.utils.text import slugify


load_dotenv()



class BaseModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='%(class)s_created'
    )
    updated_by = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='%(class)s_updated'
    )
    deleted_by = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='%(class)s_deleted'
    )
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        abstract = True

class Category(BaseModel):
    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    description = models.TextField(null=True, blank=True)

    def save(self, *args, **kwargs):
        # Generate slug if not exists
        if not self.slug:
            self.slug = slugify(self.name)
        # Save the category
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'
        ordering = ['name']


class MediaStorage(S3Boto3Storage):
    """
    Custom storage for media files using DigitalOcean Spaces.
    """
    bucket_name = os.getenv("DO_SPACE_NAME")
    custom_domain = os.getenv("CUSTOM_DOMAIN")
    location = os.getenv("LOCATION")
    file_overwrite = os.getenv("FILE_OVERWRITE")
    default_acl = os.getenv("DEFAULT_ACL")
