from django.contrib import admin
from .models import Category

# Register your models here.

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description')
    search_fields = ('name', 'description')
    list_filter = ('name', 'description')
    ordering = ('name',)

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_deleted=False)
