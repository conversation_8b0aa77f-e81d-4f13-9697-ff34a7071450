from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter

from base.models import Category
from base.serializers import CategorySerializer, CategoryCreateUpdateSerializer
from base.pagination import StandardPagination

class CategoryListCreateAPIView(APIView):
    """
    API View for listing all categories and creating new categories.
    """
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']
    pagination_class = StandardPagination

    def get_queryset(self):
        return Category.objects.filter(is_deleted=False)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        
        # Apply filters
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(request, queryset, self)
            
        # Paginate
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = CategorySerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
            
        serializer = CategorySerializer(queryset, many=True)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        serializer = CategoryCreateUpdateSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(created_by=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CategoryRetrieveUpdateAPIView(APIView):
    """
    API View for retrieving, updating categories
    """
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_object(self, slug):
        obj = get_object_or_404(Category.objects.filter(is_deleted=False), slug=slug)
        self.check_object_permissions(self.request, obj)
        return obj

    def get(self, request, slug, *args, **kwargs):
        category = self.get_object(slug)
        serializer = CategorySerializer(category)
        return Response(serializer.data)

    def put(self, request, slug, *args, **kwargs):
        category = self.get_object(slug)
        serializer = CategoryCreateUpdateSerializer(category, data=request.data)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, slug, *args, **kwargs):
        category = self.get_object(slug)
        serializer = CategoryCreateUpdateSerializer(category, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)