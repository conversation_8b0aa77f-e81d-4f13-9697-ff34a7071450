from rest_framework import serializers
from testimonies.models import Testimony

class TestimonySerializer(serializers.ModelSerializer):
    """
    Serializer for Testimony model
    """
    class Meta:
        model = Testimony
        fields = [
            'id', 'name', 'email', 'slug', 'content', 
            'image', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at']

class TestimonyCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating testimonies
    """
    class Meta:
        model = Testimony
        fields = ['name', 'email', 'content', 'image', 'status']