from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter

from testimonies.models import Testimony
from testimonies.serializers import TestimonySerializer, TestimonyCreateUpdateSerializer
from base.pagination import StandardPagination

class TestimonyListCreateAPIView(APIView):
    """
    API View for listing all testimonies and creating new testimonies.
    """
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status']
    search_fields = ['name', 'email', 'content']
    ordering_fields = ['name', 'created_at']
    ordering = ['-created_at']
    pagination_class = StandardPagination

    def get_queryset(self):
        queryset = Testimony.objects.filter(is_deleted=False)
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(status='published')
        return queryset

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(request, queryset, self)
            
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = TestimonySerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
            
        serializer = TestimonySerializer(queryset, many=True)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        serializer = TestimonyCreateUpdateSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(created_by=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TestimonyRetrieveUpdateUnpublishAPIView(APIView):
    """
    API View for retrieving, updating, and unpublishing testimonies
    """
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_object(self, pk):
        queryset = Testimony.objects.filter(is_deleted=False)
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(status='published')
        obj = get_object_or_404(queryset, pk=pk)
        self.check_object_permissions(self.request, obj)
        return obj

    def get(self, request, pk, *args, **kwargs):
        testimony = self.get_object(pk)
        serializer = TestimonySerializer(testimony)
        return Response(serializer.data)

    def put(self, request, pk, *args, **kwargs):
        testimony = self.get_object(pk)
        serializer = TestimonyCreateUpdateSerializer(testimony, data=request.data)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk, *args, **kwargs):
        testimony = self.get_object(pk)
        serializer = TestimonyCreateUpdateSerializer(testimony, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, *args, **kwargs):
        testimony = self.get_object(pk)
        testimony.status = 'draft'
        testimony.updated_by = request.user
        testimony.save()
        return Response(status=status.HTTP_204_NO_CONTENT)