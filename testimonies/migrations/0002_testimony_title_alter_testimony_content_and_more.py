# Generated by Django 4.2.23 on 2025-06-23 15:49

import base.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testimonies', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='testimony',
            name='title',
            field=models.CharField(blank=True, help_text='Role or occupation (e.g., parent, business owner)', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='testimony',
            name='content',
            field=models.TextField(help_text='The testimony content/story'),
        ),
        migrations.AlterField(
            model_name='testimony',
            name='email',
            field=models.EmailField(help_text='Contact email for follow-up if needed', max_length=254),
        ),
        migrations.AlterField(
            model_name='testimony',
            name='image',
            field=models.ImageField(blank=True, help_text='Optional profile image', null=True, storage=base.models.MediaStorage(), upload_to='testimonies/images/'),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='testimony',
            name='name',
            field=models.<PERSON><PERSON><PERSON><PERSON>(help_text='Full name of the person giving testimony', max_length=255),
        ),
    ]
