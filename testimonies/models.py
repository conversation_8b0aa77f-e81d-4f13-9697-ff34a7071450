from django.db import models
from base.models import BaseModel, MediaStorage
from django.utils.text import slugify


# Create your models here.
class Testimony(BaseModel):
    name = models.CharField(max_length=255)
    email = models.EmailField()
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    content = models.TextField()
    image = models.ImageField(
        storage=MediaStorage(),
        upload_to='testimonies/images/',
        blank=True,
        null=True
    )
    status = models.CharField(
        max_length=20,
        choices=[('draft', 'Draft'), ('published', 'Published')],
        default='draft'
    )

    def save(self, *args, **kwargs):
        # Generate slug if not exists
        if not self.slug:
            self.slug = slugify(self.name)
        # Save the post
        super().save(*args, **kwargs)
        
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = 'Testimony'
        verbose_name_plural = 'Testimonies'
        ordering = ['created_at']
