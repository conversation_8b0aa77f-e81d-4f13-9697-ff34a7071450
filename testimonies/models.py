from django.db import models
from base.models import BaseModel, MediaStorage
from django_ckeditor_5.fields import CKE<PERSON>or<PERSON><PERSON><PERSON>
from django.utils.text import slugify


# Create your models here.
class Testimony(BaseModel):
    name = models.CharField(max_length=255, help_text="Full name of the person giving testimony")
    title = models.CharField(
        max_length=255,
        help_text="Role or occupation (e.g., parent, business owner)",
        blank=True,
        null=True
    )
    email = models.EmailField(help_text="Contact email for follow-up if needed")
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    content = CKEditor5Field('Content', config_name='extends', help_text="The testimony content/story")
    image = models.ImageField(
        storage=MediaStorage(),
        upload_to='testimonies/images/',
        blank=True,
        null=True,
        help_text="Optional profile image"
    )
    status = models.CharField(
        max_length=20,
        choices=[('draft', 'Draft'), ('published', 'Published')],
        default='draft'
    )

    def save(self, *args, **kwargs):
        # Generate slug if not exists
        if not self.slug:
            self.slug = slugify(self.name)
        # Save the post
        super().save(*args, **kwargs)
        
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = 'Testimony'
        verbose_name_plural = 'Testimonies'
        ordering = ['created_at']
