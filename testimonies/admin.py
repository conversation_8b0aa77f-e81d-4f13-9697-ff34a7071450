from django.contrib import admin
from .models import Testimony

@admin.register(Testimony)
class TestimonyAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'email', 'status')
    search_fields = ('name', 'email')
    list_filter = ('status', 'created_at')
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_deleted=False)
# Register your models here.
