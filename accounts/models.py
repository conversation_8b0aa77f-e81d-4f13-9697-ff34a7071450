from django.db import models
from base.models import BaseModel, MediaStorage

# Author model


class Author(BaseModel):
    name = models.CharField(max_length=255)
    bio = models.TextField(null=True, blank=True)
    image = models.ImageField(upload_to='authors/', storage=MediaStorage(), null=True, blank=True)

    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = 'Author'
        verbose_name_plural = 'Authors'
        ordering = ['name']

