from rest_framework import serializers
from accounts.models import Author

class AuthorSerializer(serializers.ModelSerializer):
    """
    Serializer for Author model
    """
    class Meta:
        model = Author
        fields = ['id', 'name', 'bio', 'image', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

class AuthorCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating authors
    """
    class Meta:
        model = Author
        fields = ['name', 'bio', 'image']