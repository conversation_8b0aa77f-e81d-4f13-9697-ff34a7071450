from django.db import models
from base.models import BaseModel
from django.utils.text import slugify


# Subscription model for newsletter/updates
class Subscription(BaseModel):
    first_name = models.Char<PERSON><PERSON>(
        max_length=255, 
        help_text="First name of the subscriber"
    )
    last_name = models.Char<PERSON><PERSON>(
        max_length=255, 
        help_text="Last name of the subscriber"
    )
    email = models.EmailField(
        unique=True,
        help_text="Email address for subscription (must be unique)"
    )
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    
    # Many-to-many relationship with categories for interests
    interests = models.ManyToManyField(
        'base.Category', 
        blank=True, 
        related_name='subscription_interests',
        help_text="Categories the subscriber is interested in"
    )
    
    # Subscription status
    is_active = models.Bo<PERSON>anField(
        default=True,
        help_text="Whether the subscription is active"
    )
    
    # Confirmation status
    is_confirmed = models.<PERSON><PERSON>anField(
        default=False,
        help_text="Whether the email has been confirmed"
    )
    
    # Unsubscribe token for easy unsubscribing
    unsubscribe_token = models.<PERSON><PERSON><PERSON><PERSON>(
        max_length=100,
        unique=True,
        blank=True,
        null=True,
        help_text="Token for unsubscribing without login"
    )
    
    # Subscription date tracking
    subscribed_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When the user subscribed"
    )
    
    unsubscribed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the user unsubscribed"
    )

    def save(self, *args, **kwargs):
        # Generate slug from full name if not exists
        if not self.slug:
            full_name = f"{self.first_name} {self.last_name}"
            self.slug = slugify(full_name)
            
        # Generate unsubscribe token if not exists
        if not self.unsubscribe_token:
            import uuid
            self.unsubscribe_token = str(uuid.uuid4())
            
        super().save(*args, **kwargs)
        
    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    def get_interests_list(self):
        """Return list of interest category names"""
        return list(self.interests.values_list('name', flat=True))
    
    def unsubscribe(self):
        """Mark subscription as inactive and set unsubscribed date"""
        self.is_active = False
        from django.utils import timezone
        self.unsubscribed_at = timezone.now()
        self.save()
    
    class Meta:
        verbose_name = 'Subscription'
        verbose_name_plural = 'Subscriptions'
        ordering = ['-subscribed_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['is_active']),
            models.Index(fields=['unsubscribe_token']),
        ]
