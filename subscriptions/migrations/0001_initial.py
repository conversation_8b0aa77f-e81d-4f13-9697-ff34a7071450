# Generated by Django 4.2.23 on 2025-06-23 16:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('base', '0004_make_slug_unique'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('first_name', models.CharField(help_text='First name of the subscriber', max_length=255)),
                ('last_name', models.Char<PERSON>ield(help_text='Last name of the subscriber', max_length=255)),
                ('email', models.EmailField(help_text='Email address for subscription (must be unique)', max_length=254, unique=True)),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether the subscription is active')),
                ('is_confirmed', models.BooleanField(default=False, help_text='Whether the email has been confirmed')),
                ('unsubscribe_token', models.CharField(blank=True, help_text='Token for unsubscribing without login', max_length=100, null=True, unique=True)),
                ('subscribed_at', models.DateTimeField(auto_now_add=True, help_text='When the user subscribed')),
                ('unsubscribed_at', models.DateTimeField(blank=True, help_text='When the user unsubscribed', null=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL)),
                ('interests', models.ManyToManyField(blank=True, help_text='Categories the subscriber is interested in', related_name='subscription_interests', to='base.category')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Subscription',
                'verbose_name_plural': 'Subscriptions',
                'ordering': ['-subscribed_at'],
                'indexes': [models.Index(fields=['email'], name='subscriptio_email_61716f_idx'), models.Index(fields=['is_active'], name='subscriptio_is_acti_46f9bf_idx'), models.Index(fields=['unsubscribe_token'], name='subscriptio_unsubsc_8e825b_idx')],
            },
        ),
    ]
