from django.contrib import admin
from .models import Subscription


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'first_name', 'last_name', 'email', 
        'is_active', 'is_confirmed', 'subscribed_at'
    )
    list_filter = (
        'is_active', 'is_confirmed', 'subscribed_at', 
        'interests', 'created_at'
    )
    search_fields = ('first_name', 'last_name', 'email')
    ordering = ('-subscribed_at',)
    readonly_fields = (
        'slug', 'unsubscribe_token', 'subscribed_at', 
        'unsubscribed_at', 'created_at', 'updated_at'
    )
    
    filter_horizontal = ('interests',)
    
    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'email', 'slug')
        }),
        ('Subscription Details', {
            'fields': (
                'interests', 'is_active', 'is_confirmed',
                'subscribed_at', 'unsubscribed_at'
            )
        }),
        ('System Fields', {
            'fields': (
                'unsubscribe_token', 'created_at', 'updated_at',
                'created_by', 'updated_by'
            ),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Only show non-deleted subscriptions"""
        return super().get_queryset(request).filter(is_deleted=False)
    
    actions = ['mark_as_confirmed', 'mark_as_unconfirmed', 'unsubscribe_selected']
    
    def mark_as_confirmed(self, request, queryset):
        """Mark selected subscriptions as confirmed"""
        updated = queryset.update(is_confirmed=True)
        self.message_user(
            request, 
            f'{updated} subscription(s) marked as confirmed.'
        )
    mark_as_confirmed.short_description = "Mark selected as confirmed"
    
    def mark_as_unconfirmed(self, request, queryset):
        """Mark selected subscriptions as unconfirmed"""
        updated = queryset.update(is_confirmed=False)
        self.message_user(
            request, 
            f'{updated} subscription(s) marked as unconfirmed.'
        )
    mark_as_unconfirmed.short_description = "Mark selected as unconfirmed"
    
    def unsubscribe_selected(self, request, queryset):
        """Unsubscribe selected subscriptions"""
        count = 0
        for subscription in queryset.filter(is_active=True):
            subscription.unsubscribe()
            count += 1
        
        self.message_user(
            request, 
            f'{count} subscription(s) unsubscribed.'
        )
    unsubscribe_selected.short_description = "Unsubscribe selected"
