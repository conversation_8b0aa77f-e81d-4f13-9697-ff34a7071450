from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter

from subscriptions.models import Subscription
from subscriptions.serializers import (
    SubscriptionSerializer,
    SubscriptionCreateSerializer,
    SubscriptionUpdateSerializer,
    UnsubscribeSerializer
)
from base.pagination import StandardPagination


class SubscriptionListCreateAPIView(APIView):
    """
    API View for listing subscriptions and creating new subscriptions.

    GET: List all subscriptions (admin only)
    POST: Create new subscription (public)
    """
    permission_classes = []  # No authentication required
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active', 'is_confirmed', 'interests__name']
    search_fields = ['first_name', 'last_name', 'email']
    ordering_fields = ['subscribed_at', 'first_name', 'last_name']
    ordering = ['-subscribed_at']
    pagination_class = StandardPagination

    def get_queryset(self):
        """Only authenticated users can see subscription list"""
        return Subscription.objects.filter(is_deleted=False)

    def get(self, request, *args, **kwargs):
        """List subscriptions - admin only"""
        if not request.user.is_authenticated:
            return Response(
                {"detail": "Authentication required to view subscriptions."},
                status=status.HTTP_401_UNAUTHORIZED
            )
            
        queryset = self.get_queryset()
        
        # Apply filters
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(request, queryset, self)
            
        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = SubscriptionSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
            
        serializer = SubscriptionSerializer(queryset, many=True)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        """Create new subscription - public endpoint"""
        serializer = SubscriptionCreateSerializer(data=request.data)
        if serializer.is_valid():
            subscription = serializer.save()
            
            # Return success response
            response_serializer = SubscriptionSerializer(subscription)
            return Response(
                {
                    "message": "Subscription created successfully!",
                    "subscription": response_serializer.data
                },
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SubscriptionRetrieveUpdateAPIView(APIView):
    """
    API View for retrieving and updating subscriptions
    """
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_object(self, pk):
        """Get subscription by ID"""
        obj = get_object_or_404(
            Subscription.objects.filter(is_deleted=False), 
            pk=pk
        )
        self.check_object_permissions(self.request, obj)
        return obj

    def get(self, request, pk, *args, **kwargs):
        """Get subscription details"""
        subscription = self.get_object(pk)
        serializer = SubscriptionSerializer(subscription)
        return Response(serializer.data)

    def patch(self, request, pk, *args, **kwargs):
        """Update subscription preferences"""
        subscription = self.get_object(pk)
        serializer = SubscriptionUpdateSerializer(
            subscription, 
            data=request.data, 
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            response_serializer = SubscriptionSerializer(subscription)
            return Response(response_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UnsubscribeAPIView(APIView):
    """
    API View for unsubscribing from newsletter
    """
    permission_classes = []  # Public endpoint

    def post(self, request, *args, **kwargs):
        """Unsubscribe by email"""
        serializer = UnsubscribeSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            # Find and unsubscribe
            subscription = Subscription.objects.filter(
                email=email,
                is_deleted=False,
                is_active=True
            ).first()
            
            if subscription:
                subscription.unsubscribe()
                return Response(
                    {"message": "Successfully unsubscribed from newsletter."},
                    status=status.HTTP_200_OK
                )
            
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UnsubscribeByTokenAPIView(APIView):
    """
    API View for unsubscribing using token (for email links)
    """
    permission_classes = []  # Public endpoint

    def get(self, request, token, *args, **kwargs):
        """Unsubscribe using token from email link"""
        subscription = get_object_or_404(
            Subscription.objects.filter(is_deleted=False),
            unsubscribe_token=token,
            is_active=True
        )
        
        subscription.unsubscribe()
        
        return Response(
            {
                "message": f"Successfully unsubscribed {subscription.email} from newsletter.",
                "email": subscription.email
            },
            status=status.HTTP_200_OK
        )
