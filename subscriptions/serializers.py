from rest_framework import serializers
from subscriptions.models import Subscription
from base.models import Category
from base.serializers import CategorySerializer


class SubscriptionSerializer(serializers.ModelSerializer):
    """
    Serializer for Subscription model - Read operations
    """
    interests = CategorySerializer(many=True, read_only=True)
    full_name = serializers.CharField(read_only=True)
    
    class Meta:
        model = Subscription
        fields = [
            'id', 'first_name', 'last_name', 'full_name', 'email', 
            'slug', 'interests', 'is_active', 'is_confirmed',
            'subscribed_at', 'unsubscribed_at', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'slug', 'subscribed_at', 'unsubscribed_at', 
            'created_at', 'updated_at'
        ]


class SubscriptionCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating subscriptions
    """
    interests = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.filter(is_deleted=False),
        many=True,
        required=False,
        help_text="List of category IDs the user is interested in"
    )
    
    class Meta:
        model = Subscription
        fields = ['first_name', 'last_name', 'email', 'interests']
    
    def validate_email(self, value):
        """Check if email is already subscribed and active"""
        existing = Subscription.objects.filter(
            email=value, 
            is_deleted=False,
            is_active=True
        ).first()
        
        if existing:
            raise serializers.ValidationError(
                "This email is already subscribed to our newsletter."
            )
        return value
    
    def create(self, validated_data):
        interests = validated_data.pop('interests', [])
        subscription = Subscription.objects.create(**validated_data)
        subscription.interests.set(interests)
        return subscription


class SubscriptionUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating subscription preferences
    """
    interests = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.filter(is_deleted=False),
        many=True,
        required=False
    )
    
    class Meta:
        model = Subscription
        fields = ['first_name', 'last_name', 'interests', 'is_active']
    
    def update(self, instance, validated_data):
        interests = validated_data.pop('interests', None)
        
        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Update interests if provided
        if interests is not None:
            instance.interests.set(interests)
            
        instance.save()
        return instance


class UnsubscribeSerializer(serializers.Serializer):
    """
    Serializer for unsubscribe requests
    """
    email = serializers.EmailField(
        help_text="Email address to unsubscribe"
    )
    
    def validate_email(self, value):
        """Check if email exists and is active"""
        subscription = Subscription.objects.filter(
            email=value,
            is_deleted=False,
            is_active=True
        ).first()
        
        if not subscription:
            raise serializers.ValidationError(
                "No active subscription found for this email address."
            )
        
        return value
