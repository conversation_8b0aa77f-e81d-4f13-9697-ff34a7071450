from rest_framework import serializers
from partners.models import Partner
from base.models import Category

class PartnerSerializer(serializers.ModelSerializer):
    """
    Serializer for Partner model
    """
    categories = serializers.StringRelatedField(many=True)
    
    class Meta:
        model = Partner
        fields = [
            'id', 'name', 'slug', 'description', 'image', 
            'website', 'categories', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at']

class PartnerCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating partners
    """
    categories = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(),
        many=True,
        required=False
    )

    class Meta:
        model = Partner
        fields = ['name', 'description', 'image', 'website', 'categories', 'status']

    def create(self, validated_data):
        categories = validated_data.pop('categories', [])
        partner = Partner.objects.create(**validated_data)
        partner.categories.set(categories)
        return partner

    def update(self, instance, validated_data):
        categories = validated_data.pop('categories', None)
        instance = super().update(instance, validated_data)
        if categories is not None:
            instance.categories.set(categories)
        return instance