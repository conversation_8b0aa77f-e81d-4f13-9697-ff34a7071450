from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter

from partners.models import Partner
from partners.serializers import PartnerSerializer, PartnerCreateUpdateSerializer
from base.pagination import StandardPagination

class PartnerListCreateAPIView(APIView):
    """
    API View for listing all partners and creating new partners.
    """
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['categories__name', 'status']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']
    pagination_class = StandardPagination

    def get_queryset(self):
        queryset = Partner.objects.filter(is_deleted=False)
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(status='published')
        return queryset

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(request, queryset, self)
            
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = PartnerSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
            
        serializer = PartnerSerializer(queryset, many=True)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        serializer = PartnerCreateUpdateSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(created_by=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PartnerRetrieveUpdateUnpublishAPIView(APIView):
    """
    API View for retrieving, updating, and unpublishing partners
    """
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_object(self, slug):
        queryset = Partner.objects.filter(is_deleted=False)
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(status='published')
        obj = get_object_or_404(queryset, slug=slug)
        self.check_object_permissions(self.request, obj)
        return obj

    def get(self, request, slug, *args, **kwargs):
        partner = self.get_object(slug)
        serializer = PartnerSerializer(partner)
        return Response(serializer.data)

    def put(self, request, slug, *args, **kwargs):
        partner = self.get_object(slug)
        serializer = PartnerCreateUpdateSerializer(partner, data=request.data)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, slug, *args, **kwargs):
        partner = self.get_object(slug)
        serializer = PartnerCreateUpdateSerializer(partner, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug, *args, **kwargs):
        partner = self.get_object(slug)
        partner.status = 'draft'
        partner.updated_by = request.user
        partner.save()
        return Response(status=status.HTTP_204_NO_CONTENT)