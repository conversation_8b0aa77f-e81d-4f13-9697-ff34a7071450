from django.db import models
from base.models import BaseModel, MediaStorage
from django.utils.text import slugify

# Create your models here.
class Partner(BaseModel):
    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    description = models.TextField()
    image = models.ImageField(
        storage=MediaStorage(),
        upload_to='partners/images/',
        blank=True,
        null=True
    )
    website = models.URLField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Website of the partner"
    )
    categories = models.ManyToManyField('base.Category', blank=True, related_name='partner_categories')
    status = models.CharField(
        max_length=20,
        choices=[('draft', 'Draft'), ('published', 'Published')],
        default='draft'
    )

    def save(self, *args, **kwargs):
        # Generate slug if not exists
        if not self.slug:
            self.slug = slugify(self.name)
        # Save the post
        super().save(*args, **kwargs)
        
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = 'Partner'
        verbose_name_plural = 'Partners'
        ordering = ['created_at']
