from django.contrib import admin
from .models import Partner

# Register your models here.

@admin.register(Partner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'status')
    search_fields = ('name', 'description')
    list_filter = ('status', 'created_at', 'categories')
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_deleted=False)
